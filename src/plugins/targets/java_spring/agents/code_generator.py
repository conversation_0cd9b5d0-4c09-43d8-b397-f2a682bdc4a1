"""
LLM-driven Java code generation functionality for CodeGeneratorAgent.
All naming and logic decisions delegated to LLM through templates.
"""
import json
import os
import logging
import re
from typing import Dict, List, Any, Optional

from langchain.schema import HumanMessage, SystemMessage
from jinja2 import Environment, FileSystemLoader

from llm_settings import invoke_llm
from src.plugins.targets.java_spring.tools.database_operation_detector import DatabaseOperationDetector
from src.platform.tools.ims_segment_mapper import get_ims_segment_mapper


class JavaCodeGenerator:
    """
    LLM-driven Java Spring Boot code generator from COBOL functional specifications.
    All intelligence resides in LLM through comprehensive templates.
    """

    def __init__(self, knowledge_db=None, use_optimized_prompts=True):
        """
        Initialize the Java code generator.

        Args:
            knowledge_db: Database connection for retrieving context
            use_optimized_prompts: Whether to use optimized prompts (default: True)
        """
        self.knowledge_db = knowledge_db
        self.use_optimized_prompts = use_optimized_prompts
        self.logger = logging.getLogger(__name__)
        self.ims_mapper = get_ims_segment_mapper()

        # Initialize database operation detector
        self.db_operation_detector = DatabaseOperationDetector()

        # Initialize template environment with multiple directories
        template_dirs = [
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'context'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'generation', 'services'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'generation', 'repositories'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'database_operations'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'error_handling'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'tests'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'responses'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'system_prompts')
        ]
        self.template_env = Environment(loader=FileSystemLoader(template_dirs))
        self.logger.debug(f"Initialized template environment with directories: {template_dirs}")

    def generate_chunk_code(self, chunk_doc: Dict[str, Any], dependencies: Dict[str, Any],
                            generation_state: Dict[str, Any], tools=None) -> str:
        """
        Generate Java code using LLM based on functional specification.
        Pure orchestration - all logic in templates and LLM.
        """
        try:
            program_id = chunk_doc["program_id"]
            chunk_name = chunk_doc["chunk_name"]

            self.logger.info(f"Generating Java code for {program_id}.{chunk_name}")

            # Validate documentation
            if not self._validate_documentation(chunk_doc):
                self.logger.error(f"Insufficient documentation for {program_id}.{chunk_name}")
                return ""

            # Build complete context from database
            context = self._build_complete_context(program_id, chunk_doc, dependencies, generation_state)

            # Select appropriate template based on context
            template_name = self._select_template_by_context(context)
            self.logger.info(f"Selected template: {template_name}")

            # Render templates
            system_prompt = self._render_system_prompt(context)
            user_prompt = self._render_user_prompt(template_name, context)

            # Log rendered prompts for debugging
            self.logger.info(f"=== Java Service Generation for {program_id}.{chunk_name} ===")
            self.logger.info(f"Selected template: {template_name}")
            self.logger.info(f"System Prompt:\n{system_prompt}")
            self.logger.info(f"User Prompt:\n{user_prompt}")
            self.logger.info(f"Context provided to LLM:")
            self.logger.info(f"- Available data classes: {len(context.get('java_data_classes', []))}")
            self.logger.info(f"- Variable mappings: {len(context.get('variable_java_mappings', {}))}")
            self.logger.info(f"- Business mappings: {len(context.get('business_mappings', {}))}")
            self.logger.info(f"- Existing mappings: {len(context.get('existing_mappings', {}))}")
            self.logger.info(f"- Stored mappings: {len(context.get('stored_mappings', {}))}")
            self.logger.info(f"- Service dependencies: {len(context.get('stored_mappings', {}).get('service_dependencies', {}))}")
            self.logger.info(f"- Dependencies passed: {context.get('dependencies', {})}")

            # Generate code using LLM
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            self.logger.debug(f"Sending prompt to LLM (length: {len(user_prompt)})")
            response = invoke_llm(messages)

            # Extract code and mappings
            java_code = self._extract_java_code(response)
            mappings = self._extract_mappings(response)

            if not java_code:
                self.logger.error(f"No Java code generated for {program_id}.{chunk_name}")
                return ""

            # Save mappings to database
            if tools and mappings:
                self._save_all_mappings(tools, program_id, chunk_name, mappings)

            self.logger.info(f"Successfully generated code for {program_id}.{chunk_name}")
            return java_code

        except Exception as e:
            self.logger.error(f"Error generating Java code: {str(e)}")
            return ""

    def validate_java_code(self, java_code: str, chunk_doc: Dict[str, Any]) -> Dict[str, Any]:
        """Validate generated Java code."""
        result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }

        if not java_code or len(java_code.strip()) < 50:
            result["valid"] = False
            result["errors"].append("Generated code is too short or empty")

        if "public class" not in java_code:
            result["errors"].append("No public class declaration found")
            result["valid"] = False

        if "TODO" in java_code or "PLACEHOLDER" in java_code:
            result["errors"].append("Incomplete implementation detected")
            result["valid"] = False

        return result

    def _validate_documentation(self, chunk_doc: Dict[str, Any]) -> bool:
        """Check if sufficient documentation exists."""
        functional_spec = chunk_doc.get("functional_spec", "")
        return bool(functional_spec and len(functional_spec.strip()) > 50)

    def _build_complete_context(self, program_id: str, chunk_doc: Dict[str, Any],
                                dependencies: Dict[str, Any], generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """Build complete context from database - no manual processing."""
        # Get identifiers used in this chunk
        chunk_identifiers = self._extract_chunk_identifiers(chunk_doc)

        # Create optimized functional specification for code generation if enabled
        full_functional_spec = chunk_doc.get("functional_spec", "")
        if self.use_optimized_prompts:
            optimized_functional_spec = self._create_optimized_functional_spec(full_functional_spec)
        else:
            optimized_functional_spec = full_functional_spec

        context = {
            'program_id': program_id,
            'chunk_name': chunk_doc.get("chunk_name", ""),
            'business_name': chunk_doc.get("business_name", ""),
            'business_description': chunk_doc.get("business_description", ""),
            'functional_spec': optimized_functional_spec,  # Use optimized version for code generation
            'full_functional_spec': full_functional_spec,  # Keep full version for reference
            'input_parameters': chunk_doc.get("input_parameters", []),
            'output_parameters': chunk_doc.get("output_parameters", []),
            'dependencies': dependencies
        }

        # Add database-driven context
        if self.knowledge_db:
            # Get business mappings only for identifiers in this chunk
            context['business_mappings'] = self._get_relevant_business_mappings(program_id, chunk_identifiers)
            context['existing_mappings'] = self._get_relevant_existing_mappings(program_id, chunk_identifiers)

            # Get filtered data classes for business service generation (only those used in this chunk)
            business_data_classes = self._get_available_data_classes(program_id, chunk_identifiers)
            context['java_data_classes'] = business_data_classes

            # Separate aggregate wrappers from regular classes for enhanced context
            aggregate_wrappers = [cls for cls in business_data_classes if cls.get("is_aggregate_wrapper", False)]
            regular_classes = [cls for cls in business_data_classes if not cls.get("is_aggregate_wrapper", False)]

            context['aggregate_wrapper_classes'] = aggregate_wrappers
            context['regular_data_classes'] = regular_classes
            context['has_aggregate_wrappers'] = len(aggregate_wrappers) > 0
            context['total_business_entities'] = len(business_data_classes)

            context['file_operations'] = self._detect_file_operations(chunk_doc)

            # Detect database operations in COBOL code
            context.update(self._detect_database_operations(chunk_doc))

            # Get stored comprehensive mappings for consistency
            context['stored_mappings'] = self._get_stored_comprehensive_mappings(program_id, chunk_doc.get("chunk_name"))

            # Convert current dependencies to service_dependencies format and merge with stored mappings
            context['stored_mappings'] = self._merge_current_dependencies(context['stored_mappings'], dependencies)

            # Get variables with Java mappings for identifiers used in this chunk
            context['variable_java_mappings'] = self._get_variable_java_mappings(program_id, chunk_identifiers)

            # Add IMS segment context if available
            context.update(self._get_ims_segment_context_for_chunk(chunk_doc))

        return context

    def _create_optimized_functional_spec(self, full_functional_spec: str) -> str:
        """
        Create an optimized functional specification for code generation by removing
        test cases, validation rules, and error handling tables that make prompts too large.

        Args:
            full_functional_spec: Complete functional specification with all sections

        Returns:
            str: Optimized functional specification with only essential sections
        """
        if not full_functional_spec:
            return ""

        try:
            self.logger.info("Creating optimized functional specification for code generation")

            # Split the specification into lines for processing
            lines = full_functional_spec.split('\n')
            optimized_lines = []
            skip_section = False

            # Sections to exclude from code generation prompts
            exclude_patterns = [
                r'## VALIDATION RULES AND ERROR HANDLING',
                r'## TEST CASES',
                r'\*\*Table 1: Validation Rules\*\*',
                r'\*\*Table 2: Error Handling\*\*',
                r'\| ID \| Test Category \|',  # Test case table headers
                r'\| ID \| Field/Data \|',     # Validation table headers
                r'^\| T\d+ \|',                # Test case rows
                r'^\| V\d+ \|',                # Validation rule rows
                r'^\| E\d+ \|',                # Error handling rows
            ]

            for line in lines:
                # Check if we should start skipping a section
                should_skip = False
                for pattern in exclude_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        should_skip = True
                        skip_section = True
                        self.logger.debug(f"Starting to skip section at line: {line.strip()}")
                        break

                if should_skip:
                    continue

                # Check if we've reached a new section (stop skipping)
                if skip_section and line.strip().startswith('##') and not any(re.search(pattern, line, re.IGNORECASE) for pattern in exclude_patterns):
                    skip_section = False
                    self.logger.debug(f"Resuming at new section: {line.strip()}")

                # Include the line if we're not skipping
                if not skip_section:
                    optimized_lines.append(line)

            optimized_spec = '\n'.join(optimized_lines).strip()

            # Log the optimization results
            original_length = len(full_functional_spec)
            optimized_length = len(optimized_spec)
            reduction_percent = ((original_length - optimized_length) / original_length * 100) if original_length > 0 else 0

            self.logger.info(f"Optimized functional specification: {original_length} -> {optimized_length} chars "
                           f"({reduction_percent:.1f}% reduction)")

            return optimized_spec

        except Exception as e:
            self.logger.error(f"Error creating optimized functional specification: {str(e)}")
            # Return the original if optimization fails
            return full_functional_spec

    def _merge_current_dependencies(self, stored_mappings: Dict[str, Any], dependencies: Dict[str, Any]) -> Dict[str, Any]:
        """Convert current dependencies to service_dependencies format and merge with stored mappings."""
        self.logger.info(f"Merging dependencies: {dependencies}")

        if not dependencies or 'dependent_services' not in dependencies:
            self.logger.info("No dependent_services found in dependencies")
            return stored_mappings

        dependent_services = dependencies.get('dependent_services', [])
        self.logger.info(f"Found {len(dependent_services)} dependent services to convert")

        # Initialize service_dependencies if not present
        if 'service_dependencies' not in stored_mappings:
            stored_mappings['service_dependencies'] = {}

        # Convert dependent_services to service_dependencies format
        for service_dep in dependent_services:
            cobol_chunk = service_dep.get('cobol_chunk', '')
            java_class_name = service_dep.get('java_class_name', '')
            java_method_name = service_dep.get('java_method_name', '')
            business_name = service_dep.get('business_name', '')

            self.logger.info(f"Processing dependency: {cobol_chunk} -> {java_class_name}.{java_method_name}")

            if cobol_chunk and java_class_name:
                # Convert class name to field name (camelCase)
                field_name = self._convert_class_to_field_name(java_class_name)

                # Get method signature from database if available, otherwise construct basic one
                method_signature = self._get_method_signature_for_dependency(cobol_chunk, java_method_name)

                stored_mappings['service_dependencies'][cobol_chunk] = {
                    'java_service_name': java_class_name,
                    'field_name': field_name,
                    'java_method_name': java_method_name,
                    'method_signature': method_signature,
                    'business_purpose': business_name
                }

                self.logger.info(f"Added service dependency: {cobol_chunk} -> {java_class_name}.{java_method_name}")
            else:
                self.logger.warning(f"Skipping dependency with missing info: cobol_chunk='{cobol_chunk}', java_class_name='{java_class_name}'")

        self.logger.info(f"Final service_dependencies count: {len(stored_mappings.get('service_dependencies', {}))}")
        return stored_mappings

    def _convert_class_to_field_name(self, class_name: str) -> str:
        """Convert Java class name to field name for dependency injection."""
        if not class_name:
            return ''

        # Convert from PascalCase to camelCase
        if class_name.endswith('Service'):
            field_name = class_name[0].lower() + class_name[1:]
        else:
            field_name = class_name[0].lower() + class_name[1:] + 'Service'

        return field_name

    def _get_method_signature_for_dependency(self, cobol_chunk: str, java_method_name: str) -> str:
        """Get method signature for service dependency from database or construct basic one."""
        if not self.knowledge_db:
            return f"{java_method_name}()"

        try:
            # Try to get existing method mapping from database
            program_id = cobol_chunk.split('.')[0] if '.' in cobol_chunk else cobol_chunk.split('_')[0]
            mappings = self.knowledge_db.get_comprehensive_cobol_java_mappings(program_id)

            # Look for method mappings for this chunk
            for mapping in mappings:
                if (mapping.get('mapping_type') == 'method' and
                    mapping.get('chunk_name') == cobol_chunk and
                    mapping.get('java_name') == java_method_name):
                    return mapping.get('java_type', f"{java_method_name}()")

            # If no specific mapping found, construct a basic signature
            # Try to get input/output parameters from chunk info
            chunk_info = self.knowledge_db.get_chunk_by_name(program_id, cobol_chunk)
            if chunk_info:
                input_params = chunk_info.get('input_parameters', [])
                output_params = chunk_info.get('output_parameters', [])

                # Construct parameter list
                param_list = []
                for param in input_params:
                    param_name = param.get('name', '')
                    # Try to get Java type from variable mappings
                    java_type = self._get_java_type_for_parameter(program_id, param_name)
                    param_list.append(f"{java_type} {self._convert_to_camel_case(param_name)}")

                # Determine return type
                if output_params:
                    if len(output_params) == 1:
                        return_type = self._get_java_type_for_parameter(program_id, output_params[0].get('name', ''))
                    else:
                        return_type = "Map<String, Object>"  # Multiple outputs
                else:
                    return_type = "void"

                params_str = ", ".join(param_list)
                return f"{return_type} {java_method_name}({params_str})"

        except Exception as e:
            self.logger.warning(f"Error getting method signature for {cobol_chunk}: {str(e)}")

        return f"{java_method_name}()"

    def _get_java_type_for_parameter(self, program_id: str, cobol_name: str) -> str:
        """Get Java type for a COBOL parameter from variable mappings."""
        try:
            variables = self.knowledge_db.variable_manager.get_variables_with_java_mappings(program_id)
            for var in variables:
                if var.get('name') == cobol_name:
                    return var.get('java_data_type', 'String')
        except Exception:
            pass
        return 'String'  # Default type

    def _convert_to_camel_case(self, cobol_name: str) -> str:
        """Convert COBOL name to camelCase."""
        parts = cobol_name.replace('-', '_').split('_')
        if not parts:
            return cobol_name.lower()
        return parts[0].lower() + ''.join(word.capitalize() for word in parts[1:])

    def _extract_chunk_identifiers(self, chunk_doc: Dict[str, Any]) -> List[str]:
        """Extract ALL COBOL identifiers used in this chunk."""
        identifiers = set()

        # From functional spec - extract all COBOL identifiers
        functional_spec = chunk_doc.get("functional_spec", "")

        # Enhanced pattern to catch more COBOL identifiers
        cobol_patterns = [
            r'\b[A-Z][A-Z0-9-]+\b',  # Standard COBOL names
            r'COBOL:\s*([A-Z][A-Z0-9-]+)',  # Explicit COBOL: references
            r'\(COBOL:\s*([A-Z][A-Z0-9-]+)\)',  # (COBOL: NAME) references
            r'([A-Z][A-Z0-9-]+)\s*\(COBOL:',  # NAME (COBOL: references
        ]

        for pattern in cobol_patterns:
            matches = re.findall(pattern, functional_spec)
            if isinstance(matches[0], tuple) if matches else False:
                identifiers.update([match[0] for match in matches])
            else:
                identifiers.update(matches)

        # From parameters
        for param in chunk_doc.get("input_parameters", []) + chunk_doc.get("output_parameters", []):
            if param.get("name"):
                identifiers.add(param["name"])

        # From algorithm section - extract COBOL variable references
        algorithm = chunk_doc.get("business_logic", "")
        if algorithm:
            for pattern in cobol_patterns:
                matches = re.findall(pattern, algorithm)
                if isinstance(matches[0], tuple) if matches else False:
                    identifiers.update([match[0] for match in matches])
                else:
                    identifiers.update(matches)

        # From validation rules and error handling sections
        for section in ["validation_rules", "error_handling"]:
            section_content = chunk_doc.get(section, "")
            if section_content:
                for pattern in cobol_patterns:
                    matches = re.findall(pattern, section_content)
                    if isinstance(matches[0], tuple) if matches else False:
                        identifiers.update([match[0] for match in matches])
                    else:
                        identifiers.update(matches)

        # Filter out common COBOL keywords and short names
        cobol_keywords = {
            'IF', 'THEN', 'ELSE', 'END', 'PERFORM', 'MOVE', 'ADD', 'SUBTRACT',
            'MULTIPLY', 'DIVIDE', 'COMPUTE', 'DISPLAY', 'ACCEPT', 'OPEN', 'CLOSE',
            'READ', 'WRITE', 'REWRITE', 'DELETE', 'START', 'STOP', 'EXIT',
            'CONTINUE', 'NEXT', 'SENTENCE', 'GO', 'TO', 'VARYING', 'UNTIL',
            'BY', 'FROM', 'THRU', 'THROUGH', 'WHEN', 'OTHER', 'EVALUATE',
            'ALSO', 'AND', 'OR', 'NOT', 'EQUAL', 'GREATER', 'LESS', 'THAN'
        }

        filtered_identifiers = [
            identifier for identifier in identifiers
            if len(identifier) > 2 and identifier not in cobol_keywords
        ]

        self.logger.info(f"Extracted {len(filtered_identifiers)} COBOL identifiers from chunk")
        return filtered_identifiers

    def _get_relevant_business_mappings(self, program_id: str, identifiers: List[str]) -> Dict[str, Dict[str, str]]:
        """Get business mappings only for specified identifiers."""
        if not self.knowledge_db or not identifiers:
            return {}

        try:
            all_mappings = self.knowledge_db.get_business_name_mappings(program_id)
            # Filter to only relevant identifiers
            return {k: v for k, v in all_mappings.items() if k in identifiers}
        except Exception as e:
            self.logger.error(f"Error getting business mappings: {str(e)}")
            return {}

    def _get_relevant_existing_mappings(self, program_id: str, identifiers: List[str]) -> Dict[str, str]:
        """Get existing COBOL-Java mappings only for specified identifiers."""
        if not self.knowledge_db or not identifiers:
            return {}

        try:
            all_mappings = self.knowledge_db.get_cobol_java_mappings(program_id)
            # Filter to only relevant identifiers
            return {k: v for k, v in all_mappings.items() if k in identifiers}
        except Exception as e:
            self.logger.error(f"Error getting existing mappings: {str(e)}")
            return {}

    def _get_stored_comprehensive_mappings(self, program_id: str, chunk_name: str = None) -> Dict[str, Any]:
        """Get stored comprehensive mappings for template context."""
        if not self.knowledge_db:
            return {}

        try:
            # Get comprehensive mappings for this program
            mappings = self.knowledge_db.get_data_class_mappings(program_id, chunk_name)
            self.logger.info(f"Retrieved {len(mappings)} stored mapping categories for {program_id}")
            return mappings
        except Exception as e:
            self.logger.error(f"Error getting stored comprehensive mappings: {str(e)}")
            return {}

    def _get_variable_java_mappings(self, program_id: str, identifiers: List[str]) -> Dict[str, Any]:
        """Get variables with Java mappings for specified identifiers."""
        if not self.knowledge_db or not identifiers:
            return {}

        try:
            # Get all variables with Java mappings for this program
            all_variables = self.knowledge_db.variable_manager.get_variables_with_java_mappings(program_id)

            # Filter to only variables used in this chunk
            relevant_variables = []
            for var in all_variables:
                cobol_name = var.get('name', '')
                if cobol_name in identifiers:
                    relevant_variables.append(var)

            # Organize by COBOL name for easy template access
            organized_mappings = {}
            for var in relevant_variables:
                cobol_name = var.get('name', '')
                organized_mappings[cobol_name] = {
                    'java_name': var.get('java_name', ''),
                    'java_class': var.get('java_class', ''),
                    'java_data_type': var.get('java_data_type', ''),
                    'full_qualified_name': var.get('full_qualified_name', ''),
                    'java_annotations': var.get('java_annotations', []),
                    'is_java_entity_field': var.get('is_java_entity_field', False),
                    'business_name': var.get('business_name', ''),
                    'description': var.get('description', ''),
                    'data_type': var.get('data_type', ''),
                    'java_mapping_notes': var.get('java_mapping_notes', '')
                }

            self.logger.info(f"Retrieved Java mappings for {len(organized_mappings)} variables in {program_id}")
            return organized_mappings

        except Exception as e:
            self.logger.error(f"Error getting variable Java mappings: {str(e)}")
            return {}

    def _get_ims_segment_context_for_chunk(self, chunk_doc: Dict[str, Any]) -> Dict[str, Any]:
        """Get IMS segment context for the current chunk."""
        try:
            # Get COBOL code from functional spec and any available code
            functional_spec = chunk_doc.get("functional_spec", "")
            cobol_code = chunk_doc.get("cobol_code", functional_spec)
            chunk_name = chunk_doc.get("chunk_name", "")

            # Identify IMS segments in the code
            ims_context = self.ims_mapper.get_segments_for_template_context(cobol_code)

            # Enhance with business context for identified segments
            if ims_context.get('has_ims_segments'):
                self.logger.info(f"Found IMS segments for chunk {chunk_name}: {ims_context['ims_segments']}")

                # Add enhanced service naming suggestions based on IMS segments
                primary_segment = ims_context['ims_segments'][0] if ims_context['ims_segments'] else None
                if primary_segment:
                    business_name = self.ims_mapper.get_business_name(primary_segment)
                    if business_name:
                        ims_context['suggested_service_name'] = f"{business_name.replace(' ', '')}Service"
                        ims_context['primary_segment'] = primary_segment
                        ims_context['primary_business_context'] = business_name

                        # Add JavaDoc enhancement suggestions
                        ims_context['javadoc_enhancement'] = self.ims_mapper.generate_javadoc_comment(primary_segment)
                        ims_context['traceability_comments'] = [
                            self.ims_mapper.generate_traceability_comment(segment)
                            for segment in ims_context['ims_segments']
                        ]

            return ims_context

        except Exception as e:
            self.logger.error(f"Error getting IMS segment context for chunk: {str(e)}")
            return {
                'ims_segments': [],
                'segment_business_mappings': {},
                'has_ims_segments': False
            }

    def _get_available_data_classes(self, program_id: str, chunk_identifiers: List[str] = None) -> List[Dict[str, Any]]:
        """Get available Java data classes filtered for business service generation and chunk usage."""
        if not self.knowledge_db:
            return []

        try:
            # Get all data classes
            all_data_classes = self.knowledge_db.get_java_data_structures(program_id)

            # Filter to only include business entities (aggregate wrappers and non-chunked classes)
            business_data_classes = self._filter_classes_for_business_services(all_data_classes)

            # Further filter to only include data classes that are actually used in this chunk
            if chunk_identifiers:
                relevant_data_classes = self._filter_data_classes_by_chunk_usage(business_data_classes, chunk_identifiers)
                self.logger.info(f"Filtered {len(business_data_classes)} business data classes to {len(relevant_data_classes)} classes actually used in chunk")
                return relevant_data_classes
            else:
                self.logger.info(f"Filtered {len(all_data_classes)} data classes to {len(business_data_classes)} business entities for service generation")
                return business_data_classes

        except Exception as e:
            self.logger.error(f"Error getting data classes: {str(e)}")
            return []

    def _filter_classes_for_business_services(self, data_classes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter data classes for business service generation.
        Only include aggregate wrapper classes and non-chunked classes.
        Exclude individual chunked parts to maintain business semantics.
        """
        business_classes = []

        for class_info in data_classes:
            # Include aggregate wrapper classes (these represent complete business entities)
            if class_info.get("is_aggregate_wrapper", False):
                business_classes.append(class_info)
                self.logger.debug(f"Including aggregate wrapper class: {class_info.get('java_class_name', class_info.get('name', 'Unknown'))}")
                continue

            # Include non-chunked classes (complete structures that weren't chunked)
            cobol_structure_name = class_info.get("cobol_structure_name", "")
            if not self._is_chunked_part_from_structure_info(class_info, cobol_structure_name):
                business_classes.append(class_info)
                self.logger.debug(f"Including non-chunked class: {class_info.get('java_class_name', class_info.get('name', 'Unknown'))}")
                continue

            # Exclude chunked parts (these are technical implementation details)
            self.logger.debug(f"Excluding chunked part: {class_info.get('java_class_name', class_info.get('name', 'Unknown'))} "
                            f"(structure: {cobol_structure_name})")

        self.logger.info(f"Filtered {len(data_classes)} classes to {len(business_classes)} business classes for service generation")
        return business_classes

    def _is_chunked_part_from_structure_info(self, class_info: Dict[str, Any], cobol_structure_name: str) -> bool:
        """
        Determine if a class is a chunked part (technical implementation detail)
        rather than a complete business entity.
        """
        # Check if the structure name indicates it's a chunked part
        if "_PART_" in cobol_structure_name.upper():
            return True

        # Check if the class name indicates it's a chunked part
        java_class_name = class_info.get("java_class_name", class_info.get("name", ""))
        if "_PART_" in java_class_name.upper() or "Part" in java_class_name:
            return True

        # Check if it's marked as a partial structure in metadata
        if class_info.get("is_partial_structure", False):
            return True

        # Check if the business purpose indicates it's a chunk
        business_purpose = class_info.get("business_purpose", "")
        if "chunk" in business_purpose.lower() or "part" in business_purpose.lower():
            return True

        return False

    def _filter_data_classes_by_chunk_usage(self, data_classes: List[Dict[str, Any]], chunk_identifiers: List[str]) -> List[Dict[str, Any]]:
        """
        Filter data classes to only include those that are actually referenced in the current chunk.
        A data class is considered relevant if any of its COBOL identifiers are used in the chunk.
        """
        if not chunk_identifiers:
            return data_classes

        relevant_classes = []

        for class_info in data_classes:
            is_relevant = False

            # Check if the main COBOL structure name is referenced
            cobol_structure_name = class_info.get("cobol_structure_name", "")
            if cobol_structure_name and cobol_structure_name in chunk_identifiers:
                is_relevant = True
                self.logger.debug(f"Data class {class_info.get('java_class_name', 'Unknown')} is relevant: structure name '{cobol_structure_name}' found in chunk")

            # Check if any field names from the data class are referenced
            if not is_relevant:
                cobol_fields = class_info.get("cobol_fields", [])
                for field in cobol_fields:
                    field_name = field.get("name", "") if isinstance(field, dict) else str(field)
                    if field_name and field_name in chunk_identifiers:
                        is_relevant = True
                        self.logger.debug(f"Data class {class_info.get('java_class_name', 'Unknown')} is relevant: field '{field_name}' found in chunk")
                        break

            # Check if any related COBOL names are referenced
            if not is_relevant:
                related_cobol_names = class_info.get("related_cobol_names", [])
                for cobol_name in related_cobol_names:
                    if cobol_name and cobol_name in chunk_identifiers:
                        is_relevant = True
                        self.logger.debug(f"Data class {class_info.get('java_class_name', 'Unknown')} is relevant: related name '{cobol_name}' found in chunk")
                        break

            # Check if the original COBOL copybook name is referenced
            if not is_relevant:
                copybook_name = class_info.get("copybook_name", "")
                if copybook_name and copybook_name in chunk_identifiers:
                    is_relevant = True
                    self.logger.debug(f"Data class {class_info.get('java_class_name', 'Unknown')} is relevant: copybook '{copybook_name}' found in chunk")

            # For aggregate wrappers, check if any of the component class names are relevant
            if not is_relevant and class_info.get("is_aggregate_wrapper", False):
                component_classes = class_info.get("component_classes", [])
                for component in component_classes:
                    component_cobol_name = component.get("cobol_structure_name", "") if isinstance(component, dict) else ""
                    if component_cobol_name and component_cobol_name in chunk_identifiers:
                        is_relevant = True
                        self.logger.debug(f"Aggregate wrapper {class_info.get('java_class_name', 'Unknown')} is relevant: component '{component_cobol_name}' found in chunk")
                        break

            if is_relevant:
                relevant_classes.append(class_info)
                self.logger.debug(f"Including data class: {class_info.get('java_class_name', class_info.get('name', 'Unknown'))}")
            else:
                self.logger.debug(f"Excluding unused data class: {class_info.get('java_class_name', class_info.get('name', 'Unknown'))}")

        self.logger.info(f"Filtered {len(data_classes)} data classes to {len(relevant_classes)} classes actually used in chunk")
        return relevant_classes

    def _detect_file_operations(self, chunk_doc: Dict[str, Any]) -> Dict[str, Any]:
        """Detect file operations in functional spec."""
        functional_spec = chunk_doc.get("functional_spec", "")
        file_patterns = [
            r'READ\s+(\w+)',
            r'WRITE\s+(\w+)',
            r'OPEN\s+\w+\s+(\w+)',
            r'CLOSE\s+(\w+)'
        ]

        operations = []
        for pattern in file_patterns:
            matches = re.findall(pattern, functional_spec, re.IGNORECASE)
            operations.extend(matches)

        return {
            'detected': bool(operations),
            'operations': operations,
            'requires_file_handling': bool(operations)
        }

    def _detect_database_operations(self, chunk_doc: Dict[str, Any]) -> Dict[str, Any]:
        """Detect database operations in COBOL code and prepare template context."""
        functional_spec = chunk_doc.get("functional_spec", "")
        cobol_code = chunk_doc.get("cobol_code", functional_spec)  # Use cobol_code if available, fallback to functional_spec
        chunk_name = chunk_doc.get("chunk_name", "")

        # Detect database operations using the detector
        operations = self.db_operation_detector.detect_database_operations(cobol_code, chunk_name)

        # Classify operations for template selection
        classification = self.db_operation_detector.classify_operations_for_template_selection(operations)

        # Prepare template context
        template_context = self.db_operation_detector.prepare_template_context(operations, classification)

        self.logger.info(f"Detected {len(operations)} database operations in {chunk_name}")
        if operations:
            self.logger.info(f"Primary operation type: {classification.get('primary_operation_type')}")
            self.logger.info(f"Template recommendations: {classification.get('template_recommendations', [])}")

        return template_context

    def _select_template_by_context(self, context: Dict[str, Any]) -> str:
        """Select appropriate template based on context."""
        # Check for database operations first
        if context.get('has_database_operations', False):
            primary_op_type = context.get('primary_operation_type')
            if primary_op_type in ['IMS_DLI', 'DB2_SQL']:
                # Use optimized or original database service template based on configuration
                return 'services/database_service_optimized.j2' if self.use_optimized_prompts else 'services/database_service.j2'
            elif primary_op_type == 'GSAM_FILE':
                return 'file_processing_service_optimized.j2' if self.use_optimized_prompts else 'file_processing_service.j2'

        # Check for file operations
        if context.get('file_operations', {}).get('detected'):
            return 'file_processing_service_optimized.j2' if self.use_optimized_prompts else 'file_processing_service.j2'

        # Check for data transformation
        elif 'transformation' in context.get('business_description', '').lower():
            return 'data_transformation_service.j2'

        # Default to standard business service (optimized or original based on configuration)
        else:
            return 'standard_business_service_optimized.j2' if self.use_optimized_prompts else 'standard_business_service.j2'

    def _render_system_prompt(self, context: Dict[str, Any]) -> str:
        """Render system prompt template (optimized or original based on configuration)."""
        try:
            if self.use_optimized_prompts:
                # Use optimized system prompt template for better performance
                template = self.template_env.get_template('service_generation_system_optimized.j2')
                return template.render(**context)
            else:
                # Use original comprehensive system prompt
                template = self.template_env.get_template('service_generation_system.j2')
                return template.render(**context)
        except Exception as e:
            self.logger.warning(f"Failed to load preferred system prompt, falling back: {str(e)}")
            # Fallback to original template if preferred version is not available
            template = self.template_env.get_template('service_generation_system.j2')
            return template.render(**context)

    def _render_user_prompt(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render user prompt template."""
        template = self.template_env.get_template(template_name)
        return template.render(**context)

    def _extract_java_code(self, response: str) -> str:
        """Extract Java code from LLM response."""
        java_pattern = r'```java\s*(.*?)\s*```'
        match = re.search(java_pattern, response, re.DOTALL | re.IGNORECASE)

        if match:
            return match.group(1).strip()

        return ""

    def _extract_mappings(self, response: str) -> Dict[str, Any]:
        """Extract JSON mappings from LLM response."""
        json_pattern = r'```json\s*(.*?)\s*```'
        match = re.search(json_pattern, response, re.DOTALL | re.IGNORECASE)

        if match:
            try:
                return json.loads(match.group(1).strip())
            except json.JSONDecodeError:
                self.logger.error("Failed to parse JSON mappings")

        return {}

    def _extract_paragraph_name(self, chunk_name: str) -> str:
        """Extract paragraph name from chunk name (everything after _SECT_)."""
        if '_SECT_' in chunk_name:
            return chunk_name.split('_SECT_', 1)[1]
        return chunk_name

    def _save_all_mappings(self, tools, program_id: str, chunk_name: str, mappings: Dict[str, Any]):
        """Save all comprehensive mappings to database."""
        try:
            # Extract paragraph name for COBOL mapping
            paragraph_name = self._extract_paragraph_name(chunk_name)

            # Save service info as class mapping
            if 'service_info' in mappings:
                service_info = mappings['service_info']
                service_name = service_info.get('service_name', '')

                service_mapping_info = {
                    'package': service_info.get('package', ''),
                    'business_purpose': service_info.get('business_purpose', ''),
                    'java_type': 'service_class',
                    'is_entity_field': False,
                    'has_string_constructor': False,
                    'mapping_notes': f"Generated Java service class for COBOL chunk {chunk_name}",
                    'generation_metadata': service_info
                }

                tools.save_comprehensive_cobol_java_mapping(
                    program_id, chunk_name, paragraph_name,
                    service_name, 'class', service_mapping_info
                )

            # Save method mappings with comprehensive information
            if 'method_mappings' in mappings:
                for cobol_name, mapping_info in mappings['method_mappings'].items():
                    java_method_name = mapping_info.get('java_method_name', '')

                    # Extract paragraph name from cobol_name if it contains _SECT_
                    cobol_paragraph_name = self._extract_paragraph_name(cobol_name)

                    method_mapping_info = {
                        'java_type': mapping_info.get('method_signature', ''),
                        'business_purpose': mapping_info.get('business_purpose', ''),
                        'is_entity_field': False,
                        'has_string_constructor': False,
                        'mapping_notes': f"Generated Java method from COBOL procedure {cobol_name}",
                        'generation_metadata': mapping_info
                    }

                    tools.save_comprehensive_cobol_java_mapping(
                        program_id, chunk_name, cobol_paragraph_name,
                        java_method_name, 'method', method_mapping_info
                    )

            # Save parameter mappings with comprehensive information
            if 'parameter_mappings' in mappings:
                for cobol_name, mapping_info in mappings['parameter_mappings'].items():
                    java_name = mapping_info.get('java_name', '')

                    # Extract paragraph name from cobol_name if it contains _SECT_
                    cobol_paragraph_name = self._extract_paragraph_name(cobol_name)

                    parameter_mapping_info = {
                        'java_type': mapping_info.get('java_type', ''),
                        'business_purpose': mapping_info.get('business_name', ''),
                        'is_entity_field': False,
                        'has_string_constructor': False,
                        'mapping_notes': f"Generated Java parameter from COBOL parameter {cobol_name}",
                        'generation_metadata': mapping_info
                    }

                    tools.save_comprehensive_cobol_java_mapping(
                        program_id, chunk_name, cobol_paragraph_name,
                        java_name, 'parameter', parameter_mapping_info
                    )

            # Save variable mappings with comprehensive information
            if 'variable_mappings' in mappings:
                for cobol_name, mapping_info in mappings['variable_mappings'].items():
                    java_variable_name = mapping_info.get('java_variable_name', '')

                    # Extract paragraph name from cobol_name if it contains _SECT_
                    cobol_paragraph_name = self._extract_paragraph_name(cobol_name)

                    variable_mapping_info = {
                        'business_purpose': mapping_info.get('business_name', ''),
                        'is_entity_field': False,
                        'has_string_constructor': False,
                        'mapping_notes': f"Generated Java variable from COBOL variable {cobol_name}",
                        'generation_metadata': mapping_info
                    }

                    tools.save_comprehensive_cobol_java_mapping(
                        program_id, chunk_name, cobol_paragraph_name,
                        java_variable_name, 'variable', variable_mapping_info
                    )

            # Save data class usage mappings
            if 'data_class_usage' in mappings:
                for cobol_structure, usage_info in mappings['data_class_usage'].items():
                    java_class_used = usage_info.get('java_class_used', '')

                    # Extract paragraph name from cobol_structure if it contains _SECT_
                    cobol_paragraph_name = self._extract_paragraph_name(cobol_structure)

                    usage_mapping_info = {
                        'java_type': 'data_class_reference',
                        'business_purpose': f"Usage of data class {java_class_used}",
                        'has_string_constructor': 'parseFromString' in usage_info.get('usage_pattern', ''),
                        'is_entity_field': False,
                        'mapping_notes': f"Data class usage: {usage_info.get('usage_pattern', '')}",
                        'generation_metadata': usage_info
                    }

                    tools.save_comprehensive_cobol_java_mapping(
                        program_id, chunk_name, cobol_paragraph_name,
                        java_class_used, 'class', usage_mapping_info
                    )

            # Save service dependencies for bean injection
            if 'service_dependencies' in mappings:
                for cobol_call, service_info in mappings['service_dependencies'].items():
                    java_service_name = service_info.get('java_service_name', '')

                    # Extract paragraph name from cobol_call if it contains _SECT_
                    cobol_paragraph_name = self._extract_paragraph_name(cobol_call)

                    dependency_mapping_info = {
                        'java_type': service_info.get('method_signature', ''),
                        'business_purpose': service_info.get('business_purpose', ''),
                        'is_entity_field': False,
                        'has_string_constructor': False,
                        'mapping_notes': f"Service dependency for COBOL call {cobol_call}",
                        'generation_metadata': service_info
                    }

                    tools.save_comprehensive_cobol_java_mapping(
                        program_id, chunk_name, cobol_paragraph_name,
                        java_service_name, 'service_dependency', dependency_mapping_info
                    )

            self.logger.info(f"Saved comprehensive mappings for {program_id}.{chunk_name}")

        except Exception as e:
            self.logger.error(f"Error saving comprehensive mappings: {str(e)}")